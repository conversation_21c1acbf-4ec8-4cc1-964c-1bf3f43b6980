package com.yzedulife.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.QuestionDTO;
import com.yzedulife.service.entity.Question;
import com.yzedulife.service.mapper.QuestionMapper;
import com.yzedulife.service.mapper.QuestionOptionMapper;
import com.yzedulife.service.service.impl.QuestionServiceImpl;
import com.yzedulife.util.TestDataFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("题目服务测试")
class QuestionServiceTest {

    @Mock
    private QuestionMapper questionMapper;

    @Mock
    private QuestionOptionMapper questionOptionMapper;

    @InjectMocks
    private QuestionServiceImpl questionService;

    private Question testQuestion;
    private QuestionDTO testQuestionDTO;
    private List<Question> testQuestionList;

    @BeforeEach
    void setUp() {
        testQuestionDTO = TestDataFactory.createQuestionDTO();
        testQuestion = new Question();
        testQuestion.setId(testQuestionDTO.getId());
        testQuestion.setPageId(testQuestionDTO.getPageId());
        testQuestion.setContentType(testQuestionDTO.getContentType());
        testQuestion.setContent(testQuestionDTO.getContent());
        testQuestion.setDisplayOrder(testQuestionDTO.getDisplayOrder());
        testQuestion.setCorrectAnswerCode(testQuestionDTO.getCorrectAnswerCode());
        testQuestion.setCategory(testQuestionDTO.getCategory());

        testQuestionList = new ArrayList<>();
        testQuestionList.add(testQuestion);
        
        Question question2 = new Question();
        question2.setId(2L);
        question2.setPageId(1L);
        question2.setContentType("TEXT");
        question2.setContent("测试题目2");
        question2.setDisplayOrder(2);
        testQuestionList.add(question2);
    }

    @Test
    @DisplayName("根据ID获取题目 - 成功")
    void getById_Success() throws BusinessException {
        // Given
        when(questionMapper.selectById(1L)).thenReturn(testQuestion);

        // When
        QuestionDTO result = questionService.getById(1L);

        // Then
        assertNotNull(result);
        assertEquals(testQuestionDTO.getId(), result.getId());
        assertEquals(testQuestionDTO.getPageId(), result.getPageId());
        assertEquals(testQuestionDTO.getContentType(), result.getContentType());
        assertEquals(testQuestionDTO.getContent(), result.getContent());
        assertEquals(testQuestionDTO.getDisplayOrder(), result.getDisplayOrder());

        verify(questionMapper).selectById(1L);
    }

    @Test
    @DisplayName("根据ID获取题目 - ID为空")
    void getById_NullId() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> questionService.getById(null));
        
        assertEquals("题目ID不能为空", exception.getMessage());
        verify(questionMapper, never()).selectById(any());
    }

    @Test
    @DisplayName("根据ID获取题目 - 题目不存在")
    void getById_QuestionNotFound() {
        // Given
        when(questionMapper.selectById(999L)).thenReturn(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> questionService.getById(999L));
        
        assertEquals("题目不存在", exception.getMessage());
        verify(questionMapper).selectById(999L);
    }

    @Test
    @DisplayName("创建题目 - 成功")
    void create_Success() throws BusinessException {
        // Given
        when(questionMapper.insert(any(Question.class))).thenReturn(1);

        // When
        QuestionDTO result = questionService.create(testQuestionDTO);

        // Then
        assertNotNull(result);
        assertEquals(testQuestionDTO.getPageId(), result.getPageId());
        assertEquals(testQuestionDTO.getContentType(), result.getContentType());
        assertEquals(testQuestionDTO.getContent(), result.getContent());
        assertEquals(testQuestionDTO.getDisplayOrder(), result.getDisplayOrder());

        verify(questionMapper).insert(any(Question.class));
    }

    @Test
    @DisplayName("创建题目 - 题目信息为空")
    void create_NullQuestion() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> questionService.create(null));
        
        assertEquals("题目信息不能为空", exception.getMessage());
        verify(questionMapper, never()).insert(any(Question.class));
    }

    @Test
    @DisplayName("创建题目 - 页面ID为空")
    void create_NullPageId() {
        // Given
        testQuestionDTO.setPageId(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> questionService.create(testQuestionDTO));

        assertEquals("页面ID不能为空", exception.getMessage());
        verify(questionMapper, never()).insert(any(Question.class));
    }

    @Test
    @DisplayName("创建题目 - 题目内容为空")
    void create_EmptyContent() {
        // Given
        testQuestionDTO.setContent("");

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> questionService.create(testQuestionDTO));

        assertEquals("题目内容不能为空", exception.getMessage());
        verify(questionMapper, never()).insert(any(Question.class));
    }

    @Test
    @DisplayName("创建题目 - 内容类型为空")
    void create_EmptyContentType() {
        // Given
        testQuestionDTO.setContentType("");

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> questionService.create(testQuestionDTO));

        assertEquals("题目内容类型不能为空", exception.getMessage());
        verify(questionMapper, never()).insert(any(Question.class));
    }

    @Test
    @DisplayName("创建题目 - 数据库插入失败")
    void create_InsertFailed() {
        // Given
        when(questionMapper.insert(any(Question.class))).thenReturn(0);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> questionService.create(testQuestionDTO));
        
        assertEquals("创建题目失败", exception.getMessage());
        verify(questionMapper).insert(any(Question.class));
    }

    @Test
    @DisplayName("更新题目 - 成功")
    void update_Success() throws BusinessException {
        // Given
        when(questionMapper.selectById(1L)).thenReturn(testQuestion);
        when(questionMapper.updateById(any(Question.class))).thenReturn(1);

        // When
        QuestionDTO result = questionService.update(testQuestionDTO);

        // Then
        assertNotNull(result);
        assertEquals(testQuestionDTO.getId(), result.getId());
        assertEquals(testQuestionDTO.getContent(), result.getContent());

        verify(questionMapper).selectById(1L);
        verify(questionMapper).updateById(any(Question.class));
    }

    @Test
    @DisplayName("更新题目 - 题目信息为空")
    void update_NullQuestion() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> questionService.update(null));
        
        assertEquals("题目信息不能为空", exception.getMessage());
        verify(questionMapper, never()).selectById(any());
        verify(questionMapper, never()).updateById(any(Question.class));
    }

    @Test
    @DisplayName("更新题目 - ID为空")
    void update_NullId() {
        // Given
        testQuestionDTO.setId(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> questionService.update(testQuestionDTO));

        assertEquals("题目ID不能为空", exception.getMessage());
        verify(questionMapper, never()).selectById(any());
        verify(questionMapper, never()).updateById(any(Question.class));
    }

    @Test
    @DisplayName("更新题目 - 题目不存在")
    void update_QuestionNotFound() {
        // Given
        when(questionMapper.selectById(1L)).thenReturn(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> questionService.update(testQuestionDTO));

        assertEquals("题目不存在", exception.getMessage());
        verify(questionMapper).selectById(1L);
        verify(questionMapper, never()).updateById(any(Question.class));
    }

    @Test
    @DisplayName("删除题目 - 成功")
    void deleteById_Success() throws BusinessException {
        // Given
        when(questionMapper.selectById(1L)).thenReturn(testQuestion);
        when(questionMapper.deleteById(1L)).thenReturn(1);

        // When
        Boolean result = questionService.deleteById(1L);

        // Then
        assertTrue(result);
        verify(questionMapper).selectById(1L);
        verify(questionMapper).deleteById(1L);
    }

    @Test
    @DisplayName("删除题目 - ID为空")
    void deleteById_NullId() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> questionService.deleteById(null));
        
        assertEquals("题目ID不能为空", exception.getMessage());
        verify(questionMapper, never()).selectById(any());
        verify(questionMapper, never()).deleteById(any());
    }

    @Test
    @DisplayName("删除题目 - 题目不存在")
    void deleteById_QuestionNotFound() {
        // Given
        when(questionMapper.selectById(1L)).thenReturn(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> questionService.deleteById(1L));
        
        assertEquals("题目不存在", exception.getMessage());
        verify(questionMapper).selectById(1L);
        verify(questionMapper, never()).deleteById(any());
    }

    @Test
    @DisplayName("根据页面ID获取题目列表 - 成功")
    void getByPageId_Success() throws BusinessException {
        // Given
        when(questionMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(testQuestionList);

        // When
        List<QuestionDTO> result = questionService.getByPageId(1L);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("测试题目", result.get(0).getContent());
        assertEquals("测试题目2", result.get(1).getContent());

        verify(questionMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("根据页面ID获取题目列表 - 页面ID为空")
    void getByPageId_NullPageId() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> questionService.getByPageId(null));
        
        assertEquals("页面ID不能为空", exception.getMessage());
        verify(questionMapper, never()).selectList(any());
    }

    @Test
    @DisplayName("获取页面题目数量 - 成功")
    void getCountByPageId_Success() throws BusinessException {
        // Given
        when(questionMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(5L);

        // When
        Integer result = questionService.getCountByPageId(1L);

        // Then
        assertEquals(5, result);
        verify(questionMapper).selectCount(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("获取页面题目数量 - 页面ID为空")
    void getCountByPageId_NullPageId() {
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> questionService.getCountByPageId(null));
        
        assertEquals("页面ID不能为空", exception.getMessage());
        verify(questionMapper, never()).selectCount(any());
    }
}
