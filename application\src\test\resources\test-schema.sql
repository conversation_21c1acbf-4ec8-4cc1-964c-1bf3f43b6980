-- 测试环境数据库初始化脚本
SET NAMES utf8mb4;

-- ===========================
-- 用户管理表 (User Management)
-- ===========================

-- 管理员表
CREATE TABLE IF NOT EXISTS `admin` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
    `username` VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名 (唯一)',
    `password` VARCHAR(255) NOT NULL COMMENT '密码',
    `role` VARCHAR(20) NOT NULL COMMENT '角色',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
);

-- 学生学校表
CREATE TABLE IF NOT EXISTS `student_schools` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '学校ID',
    `school_name` VARCHAR(100) NOT NULL UNIQUE COMMENT '学校名称 (唯一)',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_school_name` (`school_name`)
);

-- 学生班级表
CREATE TABLE IF NOT EXISTS `student_classes` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '班级ID',
    `class_name` VARCHAR(100) NOT NULL UNIQUE COMMENT '班级名称 (唯一)',
    `school_id` BIGINT NULL COMMENT '学校ID',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_class_name` (`class_name`),
    INDEX `idx_school_id` (`school_id`),
    FOREIGN KEY (`school_id`) REFERENCES `student_schools` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
);

-- 学生用户表
CREATE TABLE IF NOT EXISTS `student_users` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '学生ID',
    `name` VARCHAR(50) NOT NULL COMMENT '姓名',
    `student_number` VARCHAR(20) NOT NULL COMMENT '学号',
    `class_id` BIGINT NOT NULL COMMENT '班级ID',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_student_number` (`student_number`),
    INDEX `idx_class_id` (`class_id`)
);

-- 社会人士用户表
CREATE TABLE IF NOT EXISTS `other_users` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '社会人士ID',
    `phone` VARCHAR(11) NOT NULL UNIQUE COMMENT '手机号 (唯一)',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
);

-- ===========================
-- 问卷与内容表 (Questionnaire & Content)
-- ===========================

-- 问卷表
CREATE TABLE IF NOT EXISTS `questionnaire` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '问卷ID',
    `title` VARCHAR(200) NOT NULL COMMENT '问卷标题',
    `description` TEXT COMMENT '问卷描述',
    `creator_id` BIGINT NOT NULL COMMENT '创建者ID',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
);

-- 问卷页面表
CREATE TABLE IF NOT EXISTS `questionnaire_page` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '页面ID',
    `questionnaire_id` BIGINT NOT NULL COMMENT '问卷ID',
    `title` VARCHAR(200) NOT NULL COMMENT '页面标题',
    `display_order` INT NOT NULL COMMENT '显示顺序',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
);

-- 题目表
CREATE TABLE IF NOT EXISTS `questions` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '题目ID',
    `page_id` BIGINT NOT NULL COMMENT '所属页面ID',
    `content_type` VARCHAR(10) NOT NULL COMMENT '题干类型 (TEXT/IMAGE)',
    `content` TEXT NOT NULL COMMENT '题干内容',
    `display_order` INT NOT NULL COMMENT '显示顺序',
    `correct_answer_code` VARCHAR(10) NULL COMMENT '正确答案选项代号 (nullable)',
    `category` VARCHAR(50) NULL COMMENT '题目分类 (nullable)',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_page_id` (`page_id`),
    INDEX `idx_display_order` (`display_order`)
);

-- 题目选项表
CREATE TABLE IF NOT EXISTS `question_option` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '选项ID',
    `question_id` BIGINT NOT NULL COMMENT '题目ID',
    `option_code` VARCHAR(10) NOT NULL COMMENT '选项代号',
    `option_text` VARCHAR(500) NOT NULL COMMENT '选项文本',
    `display_order` INT NOT NULL COMMENT '显示顺序',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
);

-- ===========================
-- 答案表 (Answer)
-- ===========================

-- 答卷表
CREATE TABLE IF NOT EXISTS `answer_sheet` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '答卷ID',
    `questionnaire_id` BIGINT NOT NULL COMMENT '问卷ID',
    `user_type` VARCHAR(20) NOT NULL COMMENT '用户类型',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `submitted_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
    PRIMARY KEY (`id`)
);

-- 答案详情表
CREATE TABLE IF NOT EXISTS `answer_details` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '答案详情ID',
    `answer_sheet_id` BIGINT NOT NULL COMMENT '所属答卷ID',
    `question_id` BIGINT NOT NULL COMMENT '题目ID',
    `chosen_option_code` VARCHAR(10) NOT NULL COMMENT '所选选项代号',
    `is_correct` BOOLEAN NULL COMMENT '是否正确 (nullable)',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_answer_sheet_id` (`answer_sheet_id`),
    INDEX `idx_question_id` (`question_id`),
    INDEX `idx_chosen_option_code` (`chosen_option_code`),
    UNIQUE KEY `uk_answer_question` (`answer_sheet_id`, `question_id`)
);
