package com.yzedulife.service.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yzedulife.service.convert.OtherUserConvert;
import com.yzedulife.service.dto.OtherUserDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 社会人士用户实体类
 */
@Data
@TableName("other_users")
public class OtherUser implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 社会人士ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 手机号 (唯一)
     */
    @TableField("phone")
    private String phone;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    private LocalDateTime updatedTime;

    public OtherUserDTO toDTO() {
        return OtherUserConvert.INSTANCE.entity2dto(this);
    }
}
