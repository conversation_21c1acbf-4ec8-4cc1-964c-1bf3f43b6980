package com.yzedulife.service.impl;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.ExcelService;
import com.yzedulife.service.dto.*;
import com.yzedulife.service.service.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.FileOutputStream;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Excel处理服务实现类
 */
@Service
public class ExcelServiceImpl implements ExcelService {

    @Autowired
    private StudentUserService studentUserService;

    @Autowired
    private AnswerSheetService answerSheetService;

    @Autowired
    private AnswerDetailService answerDetailService;

    @Autowired
    private QuestionService questionService;

    @Autowired
    private QuestionnaireService questionnaireService;

    @Autowired
    private QuestionOptionService questionOptionService;

    @Autowired
    private QuestionnairePageService questionnairePageService;

    @Override
    public List<StudentImportDTO> importStudentsFromExcel(InputStream inputStream, String fileName) throws BusinessException {
        List<StudentImportDTO> importResults = new ArrayList<>();
        
        try {
            Workbook workbook = createWorkbook(inputStream, fileName);
            Sheet sheet = workbook.getSheetAt(0);
            
            // 解析表头，获取字段位置映射
            Map<String, Integer> fieldIndexMap = parseHeader(sheet);
            
            // 从第二行开始处理数据
            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) continue;
                
                StudentImportDTO importDTO = parseRowData(row, fieldIndexMap, rowIndex + 1);
                if (importDTO != null) {
                    importResults.add(importDTO);
                }
            }
            
            workbook.close();
            
        } catch (Exception e) {
            throw new BusinessException("Excel文件解析失败: " + e.getMessage());
        }
        
        return importResults;
    }

    /**
     * 创建工作簿
     */
    private Workbook createWorkbook(InputStream inputStream, String fileName) throws Exception {
        if (fileName.endsWith(".xlsx")) {
            return new XSSFWorkbook(inputStream);
        } else if (fileName.endsWith(".xls")) {
            return new HSSFWorkbook(inputStream);
        } else {
            throw new BusinessException("不支持的文件格式，请使用.xls或.xlsx格式");
        }
    }

    /**
     * 解析表头
     */
    private Map<String, Integer> parseHeader(Sheet sheet) throws BusinessException {
        Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            throw new BusinessException("Excel文件表头不能为空");
        }

        Map<String, Integer> fieldIndexMap = new HashMap<>();
        for (int cellIndex = 0; cellIndex < headerRow.getLastCellNum(); cellIndex++) {
            Cell cell = headerRow.getCell(cellIndex);
            if (cell != null) {
                String headerValue = getCellStringValue(cell).trim();
                fieldIndexMap.put(headerValue, cellIndex);
            }
        }

        // 验证必需的字段是否存在
        String[] requiredFields = {"学校", "班级", "学号", "姓名"};
        for (String field : requiredFields) {
            if (!fieldIndexMap.containsKey(field)) {
                throw new BusinessException("Excel文件缺少必需的字段: " + field);
            }
        }

        return fieldIndexMap;
    }

    /**
     * 解析行数据
     */
    private StudentImportDTO parseRowData(Row row, Map<String, Integer> fieldIndexMap, int rowNumber) {
        try {
            String schoolName = getCellStringValue(row.getCell(fieldIndexMap.get("学校")));
            String className = getCellStringValue(row.getCell(fieldIndexMap.get("班级")));
            String studentNumber = getCellStringValue(row.getCell(fieldIndexMap.get("学号")));
            String name = getCellStringValue(row.getCell(fieldIndexMap.get("姓名")));
            
            // 跳过空行
            if (!StringUtils.hasText(schoolName) && !StringUtils.hasText(className) && 
                !StringUtils.hasText(studentNumber) && !StringUtils.hasText(name)) {
                return null;
            }
            
            StudentImportDTO importDTO = new StudentImportDTO();
            importDTO.setRowNumber(rowNumber);
            importDTO.setSchoolName(schoolName);
            importDTO.setClassName(className);
            importDTO.setStudentNumber(studentNumber);
            importDTO.setName(name);
            
            return importDTO;
            
        } catch (Exception e) {
            // 如果解析失败，返回一个包含错误信息的DTO
            StudentImportDTO errorDTO = new StudentImportDTO();
            errorDTO.setRowNumber(rowNumber);
            errorDTO.setErrorMessage("行数据解析失败: " + e.getMessage());
            return errorDTO;
        }
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 处理数字，避免科学计数法
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue().trim();
                } catch (Exception e) {
                    return String.valueOf(cell.getNumericCellValue());
                }
            default:
                return "";
        }
    }

    @Override
    public String exportClassAnswerData(Long classId, List<Long> questionnaireIds) throws BusinessException {
        if (classId == null) {
            throw new BusinessException("班级ID不能为空");
        }
        if (questionnaireIds == null || questionnaireIds.size() != 5) {
            throw new BusinessException("必须提供5个问卷ID");
        }

        try {
            // 获取班级学生列表
            List<StudentUserDTO> students = studentUserService.getByClassId(classId);
            if (students.isEmpty()) {
                throw new BusinessException("该班级没有学生");
            }

            // 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();

            // 获取所有问卷的题目信息（用于RAW表）
            List<QuestionInfo> allQuestions = getAllQuestionsInfo(questionnaireIds);

            // 创建RAW表（第一个表）
            Sheet rawSheet = workbook.createSheet("RAW");
            createRawSheetHeader(rawSheet, allQuestions);
            fillRawSheetData(rawSheet, students, questionnaireIds, allQuestions);

            // 创建DATA表（第二个表）
            Sheet dataSheet = workbook.createSheet("DATA");
            createDataSheetHeader(dataSheet);
            fillDataSheetData(dataSheet, students, questionnaireIds);

            // 创建ANALYSIS表（第三个表）
            Sheet analysisSheet = workbook.createSheet("ANALYSIS");
            createAnalysisSheetHeader(analysisSheet);
            fillAnalysisSheetData(analysisSheet, students, questionnaireIds);

            // 保存文件
            String fileName = generateFileName(classId);
            String filePath = saveExcelFile(workbook, fileName);

            workbook.close();
            return filePath;

        } catch (Exception e) {
            throw new BusinessException("导出Excel失败: " + e.getMessage());
        }
    }

    /**
     * 创建DATA表表头
     */
    private void createDataSheetHeader(Sheet sheet) {
        Row headerRow = sheet.createRow(0);
        String[] headers = {"学号", "姓名", "T1A", "T1B", "T1C", "T1D", "T1E", "T1F", "T1choice", "T2", "T3", "T4", "T5"};

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);

            // 设置表头样式
            CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
            Font headerFont = sheet.getWorkbook().createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            cell.setCellStyle(headerStyle);
        }
    }

    /**
     * 填充DATA表学生数据
     */
    private void fillDataSheetData(Sheet sheet, List<StudentUserDTO> students, List<Long> questionnaireIds) throws BusinessException {
        int rowIndex = 1;

        for (StudentUserDTO student : students) {
            Row row = sheet.createRow(rowIndex++);

            // 学号和姓名
            row.createCell(0).setCellValue(student.getStudentNumber());
            row.createCell(1).setCellValue(student.getName());

            // 获取学生的答题数据
            Map<String, Object> answerData = getStudentAnswerData(student.getId(), questionnaireIds);

            // T1A到T1F (T1卷子中A到F选项的个数)
            for (int i = 0; i < 6; i++) {
                String optionKey = "T1" + (char)('A' + i);
                Integer count = (Integer) answerData.getOrDefault(optionKey, 0);
                row.createCell(2 + i).setCellValue(count);
            }

            // T1choice (T1卷第二页的题目选项，转换为简称)
            String t1Choice = (String) answerData.getOrDefault("T1choice", "");
            String t1ChoiceShortName = ExcelService.CATEGORY_SHORT_NAMES.getOrDefault(t1Choice, t1Choice);
            row.createCell(8).setCellValue(t1ChoiceShortName);

            // T2到T5 (这四张卷子中答对的个数)
            for (int i = 2; i <= 5; i++) {
                String correctKey = "T" + i + "_correct";
                Integer correctCount = (Integer) answerData.getOrDefault(correctKey, 0);
                row.createCell(7 + i).setCellValue(correctCount);
            }
        }
    }

    /**
     * 获取学生答题数据
     */
    private Map<String, Object> getStudentAnswerData(Long studentId, List<Long> questionnaireIds) throws BusinessException {
        Map<String, Object> result = new HashMap<>();

        // 初始化数据
        // T1A到T1F初始化为0分
        for (char c = 'A'; c <= 'F'; c++) {
            result.put("T1" + c, 0);
        }
        result.put("T1choice", "");
        // T2到T5初始化为0个答对题目
        for (int i = 2; i <= 5; i++) {
            result.put("T" + i + "_correct", 0);
        }

        // 遍历每个问卷
        for (int i = 0; i < questionnaireIds.size(); i++) {
            Long questionnaireId = questionnaireIds.get(i);
            String questionnaireKey = "T" + (i + 1);

            // 获取学生在该问卷的答卷
            List<AnswerSheetDTO> answerSheets = answerSheetService.getByQuestionnaireId(questionnaireId);
            AnswerSheetDTO studentAnswerSheet = answerSheets.stream()
                    .filter(sheet -> studentId.equals(sheet.getStudentUserId()))
                    .findFirst()
                    .orElse(null);

            if (studentAnswerSheet != null) {
                // 获取答案详情
                List<AnswerDetailDTO> answerDetails = answerDetailService.getByAnswerSheetId(studentAnswerSheet.getId());

                if (i == 0) { // T1问卷特殊处理
                    processT1Questionnaire(result, answerDetails);
                } else { // T2-T5问卷处理答对个数
                    int correctCount = (int) answerDetails.stream()
                            .filter(detail -> Boolean.TRUE.equals(detail.getIsCorrect()))
                            .count();
                    result.put(questionnaireKey + "_correct", correctCount);
                }
            }
        }

        return result;
    }

    /**
     * 处理T1问卷数据
     */
    private void processT1Questionnaire(Map<String, Object> result, List<AnswerDetailDTO> answerDetails) throws BusinessException {
        // 统计A-F类别的总得分
        Map<String, Integer> categoryScores = new HashMap<>();
        for (char c = 'A'; c <= 'F'; c++) {
            categoryScores.put(String.valueOf(c), 0);
        }

        String t1Choice = "";

        for (AnswerDetailDTO detail : answerDetails) {
            String chosenOption = detail.getChosenOptionCode();

            // 如果是第二页的题目，记录选择
            if (isSecondPageQuestion(detail.getQuestionId())) {
                t1Choice = chosenOption;
            } else {
                // 第一页题目：获取题目信息和选项分数
                try {
                    QuestionDTO question = questionService.getById(detail.getQuestionId());
                    if (question != null && question.getCategory() != null) {
                        String category = question.getCategory();

                        // 获取选项的分数（选项内容是数字）
                        int score = getOptionScore(detail.getQuestionId(), chosenOption);

                        // 累加到对应类别的总分
                        if (category.length() == 1 && category.charAt(0) >= 'A' && category.charAt(0) <= 'F') {
                            categoryScores.put(category, categoryScores.get(category) + score);
                        }
                    }
                } catch (Exception e) {
                    // 如果获取题目信息失败，跳过该题目
                    continue;
                }
            }
        }

        // 更新结果
        for (char c = 'A'; c <= 'F'; c++) {
            result.put("T1" + c, categoryScores.get(String.valueOf(c)));
        }
        result.put("T1choice", t1Choice);
    }

    /**
     * 获取选项分数（选项内容是数字）
     */
    private int getOptionScore(Long questionId, String optionCode) {
        try {
            QuestionOptionDTO option = questionOptionService.getByQuestionIdAndOptionCode(questionId, optionCode);
            if (option != null && option.getContent() != null) {
                // 选项内容是数字，转换为分数
                return Integer.parseInt(option.getContent().trim());
            }
        } catch (Exception e) {
            // 如果解析失败，返回0分
        }
        return 0;
    }

    /**
     * 判断是否为第二页题目（需要根据实际业务逻辑实现）
     */
    private boolean isSecondPageQuestion(Long questionId) {
        // 这里需要根据实际的题目页面结构来判断
        // 可以通过查询题目信息来判断是否属于第二页
        try {
            QuestionDTO question = questionService.getById(questionId);
            // 假设第二页的页面ID为2，需要根据实际情况调整
            return question != null && question.getPageId() != null && question.getPageId() == 2L;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(Long classId) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return "class_" + classId + "_answer_data_" + timestamp + ".xlsx";
    }

    /**
     * 保存Excel文件
     */
    private String saveExcelFile(Workbook workbook, String fileName) throws Exception {
        // 创建导出目录
        String exportDir = "exports";
        Path exportPath = Paths.get(exportDir);
        if (!Files.exists(exportPath)) {
            Files.createDirectories(exportPath);
        }

        // 完整文件路径
        String filePath = exportDir + "/" + fileName;

        // 保存文件
        try (FileOutputStream fileOut = new FileOutputStream(filePath)) {
            workbook.write(fileOut);
        }

        return filePath;
    }

    /**
     * 获取所有问卷的题目信息
     */
    private List<QuestionInfo> getAllQuestionsInfo(List<Long> questionnaireIds) throws BusinessException {
        List<QuestionInfo> allQuestions = new ArrayList<>();

        for (int i = 0; i < questionnaireIds.size(); i++) {
            Long questionnaireId = questionnaireIds.get(i);
            int questionnaireIndex = i + 1; // T1, T2, T3, T4, T5
            int questionIndexInQuestionnaire = 1; // 每张卷子从Q1开始

            // 获取问卷的所有页面
            List<QuestionnairePageDTO> pages = questionnairePageService.getByQuestionnaireId(questionnaireId);

            for (QuestionnairePageDTO page : pages) {
                // 获取页面的所有题目
                List<QuestionDTO> questions = questionService.getByPageId(page.getId());

                for (QuestionDTO question : questions) {
                    String questionCode = "T" + questionnaireIndex + "Q" + questionIndexInQuestionnaire;
                    QuestionInfo questionInfo = new QuestionInfo(
                        question.getId(),
                        questionCode,
                        questionnaireIndex,
                        questionIndexInQuestionnaire
                    );
                    allQuestions.add(questionInfo);
                    questionIndexInQuestionnaire++; // 同一张卷子内延续计数
                }
            }
        }

        return allQuestions;
    }

    /**
     * 题目信息内部类
     */
    private static class QuestionInfo {
        private Long questionId;
        private String questionCode; // 如T1Q1, T1Q2等
        private Integer questionnaireIndex; // 问卷索引(1-5)
        private Integer questionIndexInQuestionnaire; // 问卷内题目索引

        public QuestionInfo(Long questionId, String questionCode, Integer questionnaireIndex, Integer questionIndexInQuestionnaire) {
            this.questionId = questionId;
            this.questionCode = questionCode;
            this.questionnaireIndex = questionnaireIndex;
            this.questionIndexInQuestionnaire = questionIndexInQuestionnaire;
        }

        // Getters
        public Long getQuestionId() { return questionId; }
        public String getQuestionCode() { return questionCode; }
        public Integer getQuestionnaireIndex() { return questionnaireIndex; }
        public Integer getQuestionIndexInQuestionnaire() { return questionIndexInQuestionnaire; }
    }

    /**
     * 分析数据内部类
     */
    private static class AnalysisData {
        private String majorChoice; // 专业选择（简称）
        private Map<String, Integer> aptitudeScores; // 资质分数
        private Map<String, Integer> motivationScores; // 兴趣分数
        private Map<String, Integer> probabilityScores; // 概率分数

        public AnalysisData() {
            this.aptitudeScores = new HashMap<>();
            this.motivationScores = new HashMap<>();
            this.probabilityScores = new HashMap<>();
        }

        // Getters and Setters
        public String getMajorChoice() { return majorChoice; }
        public void setMajorChoice(String majorChoice) { this.majorChoice = majorChoice; }
        public Map<String, Integer> getAptitudeScores() { return aptitudeScores; }
        public Map<String, Integer> getMotivationScores() { return motivationScores; }
        public Map<String, Integer> getProbabilityScores() { return probabilityScores; }
    }

    /**
     * 创建RAW表表头
     */
    private void createRawSheetHeader(Sheet sheet, List<QuestionInfo> allQuestions) {
        Row headerRow = sheet.createRow(0);

        // 前两列：学号、姓名
        headerRow.createCell(0).setCellValue("学号");
        headerRow.createCell(1).setCellValue("姓名");

        // 后续列：所有题目
        for (int i = 0; i < allQuestions.size(); i++) {
            QuestionInfo questionInfo = allQuestions.get(i);
            Cell cell = headerRow.createCell(2 + i);
            cell.setCellValue(questionInfo.getQuestionCode());

            // 设置表头样式
            CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
            Font headerFont = sheet.getWorkbook().createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            cell.setCellStyle(headerStyle);
        }

        // 设置学号、姓名列的样式
        CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
        Font headerFont = sheet.getWorkbook().createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerRow.getCell(0).setCellStyle(headerStyle);
        headerRow.getCell(1).setCellStyle(headerStyle);
    }

    /**
     * 填充RAW表数据
     */
    private void fillRawSheetData(Sheet sheet, List<StudentUserDTO> students, List<Long> questionnaireIds, List<QuestionInfo> allQuestions) throws BusinessException {
        int rowIndex = 1;

        for (StudentUserDTO student : students) {
            Row row = sheet.createRow(rowIndex++);

            // 学号和姓名
            row.createCell(0).setCellValue(student.getStudentNumber());
            row.createCell(1).setCellValue(student.getName());

            // 获取学生的所有答案
            Map<Long, String> studentAnswers = getStudentAllAnswers(student.getId(), questionnaireIds);

            // 填充每个题目的答案
            for (int i = 0; i < allQuestions.size(); i++) {
                QuestionInfo questionInfo = allQuestions.get(i);
                String answer = studentAnswers.getOrDefault(questionInfo.getQuestionId(), "");
                row.createCell(2 + i).setCellValue(answer);
            }
        }
    }

    /**
     * 获取学生所有题目的答案
     */
    private Map<Long, String> getStudentAllAnswers(Long studentId, List<Long> questionnaireIds) throws BusinessException {
        Map<Long, String> answers = new HashMap<>();

        for (Long questionnaireId : questionnaireIds) {
            // 获取学生在该问卷的答卷
            List<AnswerSheetDTO> answerSheets = answerSheetService.getByQuestionnaireId(questionnaireId);
            AnswerSheetDTO studentAnswerSheet = answerSheets.stream()
                    .filter(sheet -> studentId.equals(sheet.getStudentUserId()))
                    .findFirst()
                    .orElse(null);

            if (studentAnswerSheet != null) {
                // 获取答案详情
                List<AnswerDetailDTO> answerDetails = answerDetailService.getByAnswerSheetId(studentAnswerSheet.getId());

                for (AnswerDetailDTO detail : answerDetails) {
                    answers.put(detail.getQuestionId(), detail.getChosenOptionCode());
                }
            }
        }

        return answers;
    }

    /**
     * 创建ANALYSIS表表头
     */
    private void createAnalysisSheetHeader(Sheet sheet) {
        Row headerRow = sheet.createRow(0);
        int colIndex = 0;

        // 基本信息列
        headerRow.createCell(colIndex++).setCellValue("学号");
        headerRow.createCell(colIndex++).setCellValue("姓名");
        headerRow.createCell(colIndex++).setCellValue("专业");
        headerRow.createCell(colIndex++).setCellValue("Student choice");

        // 资质列 [au、el、ae、oa、be、me]
        for (char c = 'A'; c <= 'F'; c++) {
            String shortName = ExcelService.CATEGORY_SHORT_NAMES.get(String.valueOf(c)).toLowerCase();
            headerRow.createCell(colIndex++).setCellValue(shortName);
        }

        // 兴趣列 [am、em、aem、oam、bem、mem]
        for (char c = 'A'; c <= 'F'; c++) {
            String shortName = ExcelService.CATEGORY_SHORT_NAMES.get(String.valueOf(c));
            String motivationName = shortName.charAt(0) + "m";
            headerRow.createCell(colIndex++).setCellValue(motivationName.toLowerCase());
        }

        // 概率列 [pa、pe、pae、poa、pbe、pme]
        for (char c = 'A'; c <= 'F'; c++) {
            String shortName = ExcelService.CATEGORY_SHORT_NAMES.get(String.valueOf(c));
            String probabilityName = "p" + shortName.charAt(0);
            headerRow.createCell(colIndex++).setCellValue(probabilityName.toLowerCase());
        }

        // 评价列
        headerRow.createCell(colIndex++).setCellValue("Aptitude");
        headerRow.createCell(colIndex++).setCellValue("Motivation");
        headerRow.createCell(colIndex++).setCellValue("Evaluation");
        headerRow.createCell(colIndex++).setCellValue("AptitudeENG");
        headerRow.createCell(colIndex++).setCellValue("MotivationENG");

        // 设置表头样式
        CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
        Font headerFont = sheet.getWorkbook().createFont();
        headerFont.setBold(true);
        headerFont.setColor(IndexedColors.WHITE.getIndex()); // 白色字体
        headerStyle.setFont(headerFont);

        // 设置表头背景色 (0, 32, 96)
        headerStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 设置边框
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);

        for (int i = 0; i < colIndex; i++) {
            headerRow.getCell(i).setCellStyle(headerStyle);
        }

        // 设置列宽
        setAnalysisColumnWidths(sheet, colIndex);
    }

    /**
     * 设置ANALYSIS表列宽
     */
    private void setAnalysisColumnWidths(Sheet sheet, int totalColumns) {
        // 学号、姓名 - 标准宽度
        sheet.setColumnWidth(0, 15 * 256); // 学号
        sheet.setColumnWidth(1, 20 * 256); // 姓名

        // 专业、Student choice - 较宽
        sheet.setColumnWidth(2, 15 * 256); // 专业
        sheet.setColumnWidth(3, 30 * 256); // Student choice - 很宽，因为包含中英文全称

        // 资质列 [au、el、ae、oa、be、me] - 标准宽度
        for (int i = 4; i < 10; i++) {
            sheet.setColumnWidth(i, 12 * 256);
        }

        // 兴趣列 [am、em、aem、oam、bem、mem] - 标准宽度
        for (int i = 10; i < 16; i++) {
            sheet.setColumnWidth(i, 12 * 256);
        }

        // 概率列 [pa、pe、pae、poa、pbe、pme] - 标准宽度
        for (int i = 16; i < 22; i++) {
            sheet.setColumnWidth(i, 12 * 256);
        }

        // 评价列 - 很宽，因为包含长文本
        if (totalColumns > 22) {
            sheet.setColumnWidth(22, 30 * 256); // Aptitude
        }
        if (totalColumns > 23) {
            sheet.setColumnWidth(23, 30 * 256); // Motivation
        }
        if (totalColumns > 24) {
            sheet.setColumnWidth(24, 60 * 256); // Evaluation - 最宽，因为是两个评价的组合
        }
        if (totalColumns > 25) {
            sheet.setColumnWidth(25, 30 * 256); // AptitudeENG
        }
        if (totalColumns > 26) {
            sheet.setColumnWidth(26, 30 * 256); // MotivationENG
        }
    }

    /**
     * 填充ANALYSIS表数据
     */
    private void fillAnalysisSheetData(Sheet sheet, List<StudentUserDTO> students, List<Long> questionnaireIds) throws BusinessException {
        int rowIndex = 1;

        // 创建数据行样式
        CellStyle evenRowStyle = createDataRowStyle(sheet.getWorkbook(), true);  // #DCE6F1背景
        CellStyle oddRowStyle = createDataRowStyle(sheet.getWorkbook(), false);  // 白色背景

        for (StudentUserDTO student : students) {
            Row row = sheet.createRow(rowIndex);
            int colIndex = 0;

            // 选择样式（奇偶行交替）
            CellStyle currentRowStyle = (rowIndex % 2 == 0) ? evenRowStyle : oddRowStyle;

            // 学号和姓名
            Cell studentNumberCell = row.createCell(colIndex++);
            studentNumberCell.setCellValue(student.getStudentNumber());
            studentNumberCell.setCellStyle(currentRowStyle);

            Cell nameCell = row.createCell(colIndex++);
            nameCell.setCellValue(student.getName());
            nameCell.setCellStyle(currentRowStyle);

            // 获取学生的分析数据
            AnalysisData analysisData = calculateAnalysisData(student.getId(), questionnaireIds);

            // 专业（简称）和Student choice（全称）
            String majorShortName = analysisData.getMajorChoice();
            String majorFullName = ExcelService.CATEGORY_FULL_NAMES.getOrDefault(
                getKeyByValue(ExcelService.CATEGORY_SHORT_NAMES, majorShortName), "");

            Cell majorCell = row.createCell(colIndex++);
            majorCell.setCellValue(majorShortName);
            majorCell.setCellStyle(currentRowStyle);

            Cell majorFullCell = row.createCell(colIndex++);
            majorFullCell.setCellValue(majorFullName);
            majorFullCell.setCellStyle(currentRowStyle);

            // 资质列 [au、el、ae、oa、be、me]
            for (char c = 'A'; c <= 'F'; c++) {
                String category = String.valueOf(c);
                int aptitudeScore = analysisData.getAptitudeScores().getOrDefault(category, 0);
                Cell aptitudeCell = row.createCell(colIndex++);
                aptitudeCell.setCellValue(aptitudeScore);
                aptitudeCell.setCellStyle(currentRowStyle);
            }

            // 兴趣列 [am、em、aem、oam、bem、mem]
            for (char c = 'A'; c <= 'F'; c++) {
                String category = String.valueOf(c);
                int motivationScore = analysisData.getMotivationScores().getOrDefault(category, 0);
                Cell motivationCell = row.createCell(colIndex++);
                motivationCell.setCellValue(motivationScore);
                motivationCell.setCellStyle(currentRowStyle);
            }

            // 概率列 [pa、pe、pae、poa、pbe、pme]
            for (char c = 'A'; c <= 'F'; c++) {
                String category = String.valueOf(c);
                int probabilityScore = analysisData.getProbabilityScores().getOrDefault(category, 0);
                Cell probabilityCell = row.createCell(colIndex++);
                probabilityCell.setCellValue(probabilityScore);
                probabilityCell.setCellStyle(currentRowStyle);
            }

            // 评价列
            String selectedCategory = getKeyByValue(ExcelService.CATEGORY_SHORT_NAMES, majorShortName);
            String[] aptitudeEvaluation = getAptitudeEvaluation(
                analysisData.getAptitudeScores().getOrDefault(selectedCategory, 0));
            String[] motivationEvaluation = getMotivationEvaluation(
                analysisData.getMotivationScores().getOrDefault(selectedCategory, 0));

            Cell aptitudeCell = row.createCell(colIndex++);
            aptitudeCell.setCellValue(aptitudeEvaluation[0]); // Aptitude中文
            aptitudeCell.setCellStyle(currentRowStyle);

            Cell motivationCell = row.createCell(colIndex++);
            motivationCell.setCellValue(motivationEvaluation[0]); // Motivation中文
            motivationCell.setCellStyle(currentRowStyle);

            Cell evaluationCell = row.createCell(colIndex++);
            evaluationCell.setCellValue(aptitudeEvaluation[0] + motivationEvaluation[0]); // Evaluation
            evaluationCell.setCellStyle(currentRowStyle);

            Cell aptitudeEngCell = row.createCell(colIndex++);
            aptitudeEngCell.setCellValue(aptitudeEvaluation[1]); // AptitudeENG
            aptitudeEngCell.setCellStyle(currentRowStyle);

            Cell motivationEngCell = row.createCell(colIndex++);
            motivationEngCell.setCellValue(motivationEvaluation[1]); // MotivationENG
            motivationEngCell.setCellStyle(currentRowStyle);

            rowIndex++;
        }
    }

    /**
     * 计算学生的分析数据
     */
    private AnalysisData calculateAnalysisData(Long studentId, List<Long> questionnaireIds) throws BusinessException {
        AnalysisData analysisData = new AnalysisData();

        // 获取学生的答题数据
        Map<String, Object> studentAnswerData = getStudentAnswerData(studentId, questionnaireIds);

        // 获取T1choice作为专业选择
        String t1Choice = (String) studentAnswerData.getOrDefault("T1choice", "");
        String majorShortName = ExcelService.CATEGORY_SHORT_NAMES.getOrDefault(t1Choice, "");
        analysisData.setMajorChoice(majorShortName);

        // 计算各类别的分数
        for (char c = 'A'; c <= 'F'; c++) {
            String category = String.valueOf(c);

            // 计算资质分数 (au, el, ae, oa, be, me)
            int aptitudeScore = calculateAptitudeScore(category, studentAnswerData, questionnaireIds);
            analysisData.getAptitudeScores().put(category, aptitudeScore);

            // 计算兴趣分数 (am, em, aem, oam, bem, mem) - 就是T1中该类别的得分率
            int motivationScore = calculateMotivationScore(category, studentAnswerData);
            analysisData.getMotivationScores().put(category, motivationScore);

            // 计算概率分数 (pa, pe, pae, poa, pbe, pme)
            int probabilityScore = calculateProbabilityScore(aptitudeScore, motivationScore);
            analysisData.getProbabilityScores().put(category, probabilityScore);
        }

        return analysisData;
    }

    /**
     * 计算资质分数
     * au = T1中A类型题目的得分率*T1A权重 + T2正确率*T2A权重 + ... + T5正确率*T5A权重
     */
    private int calculateAptitudeScore(String category, Map<String, Object> studentAnswerData, List<Long> questionnaireIds) throws BusinessException {
        double totalScore = 0.0;
        int categoryIndex = category.charAt(0) - 'A'; // A=0, B=1, C=2, D=3, E=4, F=5

        // T1的得分率 * T1权重
        int t1CategoryScore = (Integer) studentAnswerData.getOrDefault("T1" + category, 0);
        int t1CategoryMaxScore = getT1CategoryMaxScore(questionnaireIds.get(0), category);
        double t1ScoreRate = t1CategoryMaxScore > 0 ? (double) t1CategoryScore / t1CategoryMaxScore * 100 : 0;
        totalScore += t1ScoreRate * ExcelService.CATEGORY_WEIGHTS[0][categoryIndex];

        // T2-T5的正确率 * 对应权重
        for (int i = 1; i < 5; i++) {
            String correctKey = "T" + (i + 1) + "_correct";
            int correctCount = (Integer) studentAnswerData.getOrDefault(correctKey, 0);
            int totalQuestions = getTotalQuestionsInQuestionnaire(questionnaireIds.get(i));
            double correctRate = totalQuestions > 0 ? (double) correctCount / totalQuestions * 100 : 0;
            totalScore += correctRate * ExcelService.CATEGORY_WEIGHTS[i][categoryIndex];
        }

        return (int) Math.round(totalScore);
    }

    /**
     * 计算兴趣分数（T1中该类别的得分率）
     */
    private int calculateMotivationScore(String category, Map<String, Object> studentAnswerData) {
        try {
            // 获取T1中该类别的得分
            int categoryScore = (Integer) studentAnswerData.getOrDefault("T1" + category, 0);

            // 这里需要获取T1问卷ID来计算最大得分
            // 由于方法签名限制，暂时返回一个基于得分的估算值
            // 实际应用中需要传入questionnaireIds参数

            // 假设每个类别的满分是100分（需要根据实际情况调整）
            int maxScore = 100;

            return maxScore > 0 ? (int) Math.round((double) categoryScore / maxScore * 100) : 0;
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 计算概率分数
     * pa = min(调整倍率 * au, 100)
     */
    private int calculateProbabilityScore(int aptitudeScore, int motivationScore) {
        double multiplier;
        if (motivationScore < 25) {
            multiplier = 0.5;
        } else if (motivationScore < 50) {
            multiplier = 0.75;
        } else if (motivationScore < 75) {
            multiplier = 1.0;
        } else {
            multiplier = 1.25;
        }

        return (int) Math.min(Math.round(multiplier * aptitudeScore), 100);
    }

    /**
     * 获取T1中某类别的最大得分
     */
    private int getT1CategoryMaxScore(Long questionnaireId, String category) throws BusinessException {
        int maxScore = 0;

        // 获取问卷的所有页面
        List<QuestionnairePageDTO> pages = questionnairePageService.getByQuestionnaireId(questionnaireId);

        for (QuestionnairePageDTO page : pages) {
            // 跳过第二页（假设第二页是选择页面）
            if (page.getPageNumber() != null && page.getPageNumber() == 2) {
                continue;
            }

            // 获取页面的所有题目
            List<QuestionDTO> questions = questionService.getByPageId(page.getId());

            for (QuestionDTO question : questions) {
                if (category.equals(question.getCategory())) {
                    // 获取该题目的最大分数（选项中的最大值）
                    List<QuestionOptionDTO> options = questionOptionService.getByQuestionId(question.getId());
                    int questionMaxScore = options.stream()
                            .mapToInt(option -> {
                                try {
                                    return Integer.parseInt(option.getContent().trim());
                                } catch (NumberFormatException e) {
                                    return 0;
                                }
                            })
                            .max()
                            .orElse(0);
                    maxScore += questionMaxScore;
                }
            }
        }

        return maxScore;
    }

    /**
     * 获取问卷的总题目数
     */
    private int getTotalQuestionsInQuestionnaire(Long questionnaireId) throws BusinessException {
        int totalQuestions = 0;

        // 获取问卷的所有页面
        List<QuestionnairePageDTO> pages = questionnairePageService.getByQuestionnaireId(questionnaireId);

        for (QuestionnairePageDTO page : pages) {
            // 获取页面的题目数量
            totalQuestions += questionService.getCountByPageId(page.getId());
        }

        return totalQuestions;
    }

    /**
     * 获取能力资质评价
     */
    private String[] getAptitudeEvaluation(int score) {
        if (score <= 20) {
            return new String[]{"学生的能力资质几乎为零。", "Very low or No aptitude."};
        } else if (score <= 40) {
            return new String[]{"学生的能力资质低。", "Low aptitude."};
        } else if (score <= 60) {
            return new String[]{"学生的整体专业能力资质欠佳。", "Not so good aptitude."};
        } else if (score <= 80) {
            return new String[]{"学生的整体专业能力资质良好。", "Good aptitude."};
        } else if (score <= 100) {
            return new String[]{"学生的整体专业能力资质非常好。", "Excellent Aptitude."};
        } else {
            return new String[]{"能力资质信息欠缺。", "Lack of information."};
        }
    }

    /**
     * 获取学习动机与兴趣评价
     */
    private String[] getMotivationEvaluation(int score) {
        if (score <= 10) {
            return new String[]{"没有学生的学习动机与兴趣。", "No interest and motivation."};
        } else if (score <= 20) {
            return new String[]{"学生的学习动机与兴趣是非常低。", "Critical low interest and motivation."};
        } else if (score <= 30) {
            return new String[]{"学生的学习动机与兴趣是低或非常低的。", "Very low interest and motivation."};
        } else if (score <= 40) {
            return new String[]{"学生的学习动机与兴趣是低。", "Low interest and motivation."};
        } else if (score <= 50) {
            return new String[]{"学生学习动机与兴趣的总体水平为中-低。", "Middle-low interest and motivation."};
        } else if (score <= 60) {
            return new String[]{"学生学习动机与兴趣的总体水平为中-高。", "Middle-high interest and motivation."};
        } else if (score <= 70) {
            return new String[]{"学生学习动机与兴趣的总体水平良好。", "Good interest and motivation."};
        } else if (score <= 100) {
            return new String[]{"学生学习动机与兴趣的总体水平非常好。", "Strong interest and motivation, determined."};
        } else {
            return new String[]{"学习兴趣与动机信息欠缺。", "Lack of information on interests."};
        }
    }

    /**
     * 根据值获取键
     */
    private String getKeyByValue(Map<String, String> map, String value) {
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (entry.getValue().equals(value)) {
                return entry.getKey();
            }
        }
        return "";
    }

    /**
     * 创建数据行样式
     */
    private CellStyle createDataRowStyle(Workbook workbook, boolean isEvenRow) {
        CellStyle style = workbook.createCellStyle();

        // 设置字体
        Font font = workbook.createFont();
        font.setColor(IndexedColors.BLACK.getIndex()); // 黑色字体
        style.setFont(font);

        // 设置背景色
        if (isEvenRow) {
            // 偶数行：#DCE6F1 (浅蓝色)
            style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        } else {
            // 奇数行：白色
            style.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        }
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置对齐方式
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        return style;
    }
}
