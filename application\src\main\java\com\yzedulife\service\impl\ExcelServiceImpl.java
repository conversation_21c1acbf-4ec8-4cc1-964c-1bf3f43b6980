package com.yzedulife.service.impl;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.ExcelService;
import com.yzedulife.service.dto.*;
import com.yzedulife.service.service.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.FileOutputStream;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Excel处理服务实现类
 */
@Service
public class ExcelServiceImpl implements ExcelService {

    @Autowired
    private StudentUserService studentUserService;

    @Autowired
    private AnswerSheetService answerSheetService;

    @Autowired
    private AnswerDetailService answerDetailService;

    @Autowired
    private QuestionService questionService;

    @Autowired
    private QuestionnaireService questionnaireService;

    @Autowired
    private QuestionOptionService questionOptionService;

    @Autowired
    private QuestionnairePageService questionnairePageService;

    @Override
    public List<StudentImportDTO> importStudentsFromExcel(InputStream inputStream, String fileName) throws BusinessException {
        List<StudentImportDTO> importResults = new ArrayList<>();
        
        try {
            Workbook workbook = createWorkbook(inputStream, fileName);
            Sheet sheet = workbook.getSheetAt(0);
            
            // 解析表头，获取字段位置映射
            Map<String, Integer> fieldIndexMap = parseHeader(sheet);
            
            // 从第二行开始处理数据
            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) continue;
                
                StudentImportDTO importDTO = parseRowData(row, fieldIndexMap, rowIndex + 1);
                if (importDTO != null) {
                    importResults.add(importDTO);
                }
            }
            
            workbook.close();
            
        } catch (Exception e) {
            throw new BusinessException("Excel文件解析失败: " + e.getMessage());
        }
        
        return importResults;
    }

    /**
     * 创建工作簿
     */
    private Workbook createWorkbook(InputStream inputStream, String fileName) throws Exception {
        if (fileName.endsWith(".xlsx")) {
            return new XSSFWorkbook(inputStream);
        } else if (fileName.endsWith(".xls")) {
            return new HSSFWorkbook(inputStream);
        } else {
            throw new BusinessException("不支持的文件格式，请使用.xls或.xlsx格式");
        }
    }

    /**
     * 解析表头
     */
    private Map<String, Integer> parseHeader(Sheet sheet) throws BusinessException {
        Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            throw new BusinessException("Excel文件表头不能为空");
        }

        Map<String, Integer> fieldIndexMap = new HashMap<>();
        for (int cellIndex = 0; cellIndex < headerRow.getLastCellNum(); cellIndex++) {
            Cell cell = headerRow.getCell(cellIndex);
            if (cell != null) {
                String headerValue = getCellStringValue(cell).trim();
                fieldIndexMap.put(headerValue, cellIndex);
            }
        }

        // 验证必需的字段是否存在
        String[] requiredFields = {"学校", "班级", "学号", "姓名"};
        for (String field : requiredFields) {
            if (!fieldIndexMap.containsKey(field)) {
                throw new BusinessException("Excel文件缺少必需的字段: " + field);
            }
        }

        return fieldIndexMap;
    }

    /**
     * 解析行数据
     */
    private StudentImportDTO parseRowData(Row row, Map<String, Integer> fieldIndexMap, int rowNumber) {
        try {
            String schoolName = getCellStringValue(row.getCell(fieldIndexMap.get("学校")));
            String className = getCellStringValue(row.getCell(fieldIndexMap.get("班级")));
            String studentNumber = getCellStringValue(row.getCell(fieldIndexMap.get("学号")));
            String name = getCellStringValue(row.getCell(fieldIndexMap.get("姓名")));
            
            // 跳过空行
            if (!StringUtils.hasText(schoolName) && !StringUtils.hasText(className) && 
                !StringUtils.hasText(studentNumber) && !StringUtils.hasText(name)) {
                return null;
            }
            
            StudentImportDTO importDTO = new StudentImportDTO();
            importDTO.setRowNumber(rowNumber);
            importDTO.setSchoolName(schoolName);
            importDTO.setClassName(className);
            importDTO.setStudentNumber(studentNumber);
            importDTO.setName(name);
            
            return importDTO;
            
        } catch (Exception e) {
            // 如果解析失败，返回一个包含错误信息的DTO
            StudentImportDTO errorDTO = new StudentImportDTO();
            errorDTO.setRowNumber(rowNumber);
            errorDTO.setErrorMessage("行数据解析失败: " + e.getMessage());
            return errorDTO;
        }
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 处理数字，避免科学计数法
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue().trim();
                } catch (Exception e) {
                    return String.valueOf(cell.getNumericCellValue());
                }
            default:
                return "";
        }
    }

    @Override
    public String exportClassAnswerData(Long classId, List<Long> questionnaireIds) throws BusinessException {
        if (classId == null) {
            throw new BusinessException("班级ID不能为空");
        }
        if (questionnaireIds == null || questionnaireIds.size() != 5) {
            throw new BusinessException("必须提供5个问卷ID");
        }

        try {
            // 获取班级学生列表
            List<StudentUserDTO> students = studentUserService.getByClassId(classId);
            if (students.isEmpty()) {
                throw new BusinessException("该班级没有学生");
            }

            // 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();

            // 获取所有问卷的题目信息（用于RAW表）
            List<QuestionInfo> allQuestions = getAllQuestionsInfo(questionnaireIds);

            // 创建RAW表（第一个表）
            Sheet rawSheet = workbook.createSheet("RAW");
            createRawSheetHeader(rawSheet, allQuestions);
            fillRawSheetData(rawSheet, students, questionnaireIds, allQuestions);

            // 创建DATA表（第二个表）
            Sheet dataSheet = workbook.createSheet("DATA");
            createDataSheetHeader(dataSheet);
            fillDataSheetData(dataSheet, students, questionnaireIds);

            // 保存文件
            String fileName = generateFileName(classId);
            String filePath = saveExcelFile(workbook, fileName);

            workbook.close();
            return filePath;

        } catch (Exception e) {
            throw new BusinessException("导出Excel失败: " + e.getMessage());
        }
    }

    /**
     * 创建DATA表表头
     */
    private void createDataSheetHeader(Sheet sheet) {
        Row headerRow = sheet.createRow(0);
        String[] headers = {"学号", "姓名", "T1A", "T1B", "T1C", "T1D", "T1E", "T1F", "T1choice", "T2", "T3", "T4", "T5"};

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);

            // 设置表头样式
            CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
            Font headerFont = sheet.getWorkbook().createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            cell.setCellStyle(headerStyle);
        }
    }

    /**
     * 填充DATA表学生数据
     */
    private void fillDataSheetData(Sheet sheet, List<StudentUserDTO> students, List<Long> questionnaireIds) throws BusinessException {
        int rowIndex = 1;

        for (StudentUserDTO student : students) {
            Row row = sheet.createRow(rowIndex++);

            // 学号和姓名
            row.createCell(0).setCellValue(student.getStudentNumber());
            row.createCell(1).setCellValue(student.getName());

            // 获取学生的答题数据
            Map<String, Object> answerData = getStudentAnswerData(student.getId(), questionnaireIds);

            // T1A到T1F (T1卷子中A到F选项的个数)
            for (int i = 0; i < 6; i++) {
                String optionKey = "T1" + (char)('A' + i);
                Integer count = (Integer) answerData.getOrDefault(optionKey, 0);
                row.createCell(2 + i).setCellValue(count);
            }

            // T1choice (T1卷第二页的题目选项)
            String t1Choice = (String) answerData.getOrDefault("T1choice", "");
            row.createCell(8).setCellValue(t1Choice);

            // T2到T5 (这四张卷子中答对的个数)
            for (int i = 2; i <= 5; i++) {
                String correctKey = "T" + i + "_correct";
                Integer correctCount = (Integer) answerData.getOrDefault(correctKey, 0);
                row.createCell(7 + i).setCellValue(correctCount);
            }
        }
    }

    /**
     * 获取学生答题数据
     */
    private Map<String, Object> getStudentAnswerData(Long studentId, List<Long> questionnaireIds) throws BusinessException {
        Map<String, Object> result = new HashMap<>();

        // 初始化数据
        // T1A到T1F初始化为0分
        for (char c = 'A'; c <= 'F'; c++) {
            result.put("T1" + c, 0);
        }
        result.put("T1choice", "");
        // T2到T5初始化为0个答对题目
        for (int i = 2; i <= 5; i++) {
            result.put("T" + i + "_correct", 0);
        }

        // 遍历每个问卷
        for (int i = 0; i < questionnaireIds.size(); i++) {
            Long questionnaireId = questionnaireIds.get(i);
            String questionnaireKey = "T" + (i + 1);

            // 获取学生在该问卷的答卷
            List<AnswerSheetDTO> answerSheets = answerSheetService.getByQuestionnaireId(questionnaireId);
            AnswerSheetDTO studentAnswerSheet = answerSheets.stream()
                    .filter(sheet -> studentId.equals(sheet.getStudentUserId()))
                    .findFirst()
                    .orElse(null);

            if (studentAnswerSheet != null) {
                // 获取答案详情
                List<AnswerDetailDTO> answerDetails = answerDetailService.getByAnswerSheetId(studentAnswerSheet.getId());

                if (i == 0) { // T1问卷特殊处理
                    processT1Questionnaire(result, answerDetails);
                } else { // T2-T5问卷处理答对个数
                    int correctCount = (int) answerDetails.stream()
                            .filter(detail -> Boolean.TRUE.equals(detail.getIsCorrect()))
                            .count();
                    result.put(questionnaireKey + "_correct", correctCount);
                }
            }
        }

        return result;
    }

    /**
     * 处理T1问卷数据
     */
    private void processT1Questionnaire(Map<String, Object> result, List<AnswerDetailDTO> answerDetails) throws BusinessException {
        // 统计A-F类别的总得分
        Map<String, Integer> categoryScores = new HashMap<>();
        for (char c = 'A'; c <= 'F'; c++) {
            categoryScores.put(String.valueOf(c), 0);
        }

        String t1Choice = "";

        for (AnswerDetailDTO detail : answerDetails) {
            String chosenOption = detail.getChosenOptionCode();

            // 如果是第二页的题目，记录选择
            if (isSecondPageQuestion(detail.getQuestionId())) {
                t1Choice = chosenOption;
            } else {
                // 第一页题目：获取题目信息和选项分数
                try {
                    QuestionDTO question = questionService.getById(detail.getQuestionId());
                    if (question != null && question.getCategory() != null) {
                        String category = question.getCategory();

                        // 获取选项的分数（选项内容是数字）
                        int score = getOptionScore(detail.getQuestionId(), chosenOption);

                        // 累加到对应类别的总分
                        if (category.length() == 1 && category.charAt(0) >= 'A' && category.charAt(0) <= 'F') {
                            categoryScores.put(category, categoryScores.get(category) + score);
                        }
                    }
                } catch (Exception e) {
                    // 如果获取题目信息失败，跳过该题目
                    continue;
                }
            }
        }

        // 更新结果
        for (char c = 'A'; c <= 'F'; c++) {
            result.put("T1" + c, categoryScores.get(String.valueOf(c)));
        }
        result.put("T1choice", t1Choice);
    }

    /**
     * 获取选项分数（选项内容是数字）
     */
    private int getOptionScore(Long questionId, String optionCode) {
        try {
            QuestionOptionDTO option = questionOptionService.getByQuestionIdAndOptionCode(questionId, optionCode);
            if (option != null && option.getContent() != null) {
                // 选项内容是数字，转换为分数
                return Integer.parseInt(option.getContent().trim());
            }
        } catch (Exception e) {
            // 如果解析失败，返回0分
        }
        return 0;
    }

    /**
     * 判断是否为第二页题目（需要根据实际业务逻辑实现）
     */
    private boolean isSecondPageQuestion(Long questionId) {
        // 这里需要根据实际的题目页面结构来判断
        // 可以通过查询题目信息来判断是否属于第二页
        try {
            QuestionDTO question = questionService.getById(questionId);
            // 假设第二页的页面ID为2，需要根据实际情况调整
            return question != null && question.getPageId() != null && question.getPageId() == 2L;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(Long classId) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return "class_" + classId + "_answer_data_" + timestamp + ".xlsx";
    }

    /**
     * 保存Excel文件
     */
    private String saveExcelFile(Workbook workbook, String fileName) throws Exception {
        // 创建导出目录
        String exportDir = "exports";
        Path exportPath = Paths.get(exportDir);
        if (!Files.exists(exportPath)) {
            Files.createDirectories(exportPath);
        }

        // 完整文件路径
        String filePath = exportDir + "/" + fileName;

        // 保存文件
        try (FileOutputStream fileOut = new FileOutputStream(filePath)) {
            workbook.write(fileOut);
        }

        return filePath;
    }

    /**
     * 获取所有问卷的题目信息
     */
    private List<QuestionInfo> getAllQuestionsInfo(List<Long> questionnaireIds) throws BusinessException {
        List<QuestionInfo> allQuestions = new ArrayList<>();
        int globalQuestionIndex = 1;

        for (int i = 0; i < questionnaireIds.size(); i++) {
            Long questionnaireId = questionnaireIds.get(i);
            int questionnaireIndex = i + 1; // T1, T2, T3, T4, T5

            // 获取问卷的所有页面
            List<QuestionnairePageDTO> pages = questionnairePageService.getByQuestionnaireId(questionnaireId);

            for (QuestionnairePageDTO page : pages) {
                // 获取页面的所有题目
                List<QuestionDTO> questions = questionService.getByPageId(page.getId());

                for (QuestionDTO question : questions) {
                    String questionCode = "T" + questionnaireIndex + "Q" + globalQuestionIndex;
                    QuestionInfo questionInfo = new QuestionInfo(
                        question.getId(),
                        questionCode,
                        questionnaireIndex,
                        globalQuestionIndex
                    );
                    allQuestions.add(questionInfo);
                    globalQuestionIndex++;
                }
            }
        }

        return allQuestions;
    }

    /**
     * 题目信息内部类
     */
    private static class QuestionInfo {
        private Long questionId;
        private String questionCode; // 如T1Q1, T1Q2等
        private Integer questionnaireIndex; // 问卷索引(1-5)
        private Integer globalQuestionIndex; // 全局题目索引

        public QuestionInfo(Long questionId, String questionCode, Integer questionnaireIndex, Integer globalQuestionIndex) {
            this.questionId = questionId;
            this.questionCode = questionCode;
            this.questionnaireIndex = questionnaireIndex;
            this.globalQuestionIndex = globalQuestionIndex;
        }

        // Getters
        public Long getQuestionId() { return questionId; }
        public String getQuestionCode() { return questionCode; }
        public Integer getQuestionnaireIndex() { return questionnaireIndex; }
        public Integer getGlobalQuestionIndex() { return globalQuestionIndex; }
    }

    /**
     * 创建RAW表表头
     */
    private void createRawSheetHeader(Sheet sheet, List<QuestionInfo> allQuestions) {
        Row headerRow = sheet.createRow(0);

        // 前两列：学号、姓名
        headerRow.createCell(0).setCellValue("学号");
        headerRow.createCell(1).setCellValue("姓名");

        // 后续列：所有题目
        for (int i = 0; i < allQuestions.size(); i++) {
            QuestionInfo questionInfo = allQuestions.get(i);
            Cell cell = headerRow.createCell(2 + i);
            cell.setCellValue(questionInfo.getQuestionCode());

            // 设置表头样式
            CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
            Font headerFont = sheet.getWorkbook().createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            cell.setCellStyle(headerStyle);
        }

        // 设置学号、姓名列的样式
        CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
        Font headerFont = sheet.getWorkbook().createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerRow.getCell(0).setCellStyle(headerStyle);
        headerRow.getCell(1).setCellStyle(headerStyle);
    }

    /**
     * 填充RAW表数据
     */
    private void fillRawSheetData(Sheet sheet, List<StudentUserDTO> students, List<Long> questionnaireIds, List<QuestionInfo> allQuestions) throws BusinessException {
        int rowIndex = 1;

        for (StudentUserDTO student : students) {
            Row row = sheet.createRow(rowIndex++);

            // 学号和姓名
            row.createCell(0).setCellValue(student.getStudentNumber());
            row.createCell(1).setCellValue(student.getName());

            // 获取学生的所有答案
            Map<Long, String> studentAnswers = getStudentAllAnswers(student.getId(), questionnaireIds);

            // 填充每个题目的答案
            for (int i = 0; i < allQuestions.size(); i++) {
                QuestionInfo questionInfo = allQuestions.get(i);
                String answer = studentAnswers.getOrDefault(questionInfo.getQuestionId(), "");
                row.createCell(2 + i).setCellValue(answer);
            }
        }
    }

    /**
     * 获取学生所有题目的答案
     */
    private Map<Long, String> getStudentAllAnswers(Long studentId, List<Long> questionnaireIds) throws BusinessException {
        Map<Long, String> answers = new HashMap<>();

        for (Long questionnaireId : questionnaireIds) {
            // 获取学生在该问卷的答卷
            List<AnswerSheetDTO> answerSheets = answerSheetService.getByQuestionnaireId(questionnaireId);
            AnswerSheetDTO studentAnswerSheet = answerSheets.stream()
                    .filter(sheet -> studentId.equals(sheet.getStudentUserId()))
                    .findFirst()
                    .orElse(null);

            if (studentAnswerSheet != null) {
                // 获取答案详情
                List<AnswerDetailDTO> answerDetails = answerDetailService.getByAnswerSheetId(studentAnswerSheet.getId());

                for (AnswerDetailDTO detail : answerDetails) {
                    answers.put(detail.getQuestionId(), detail.getChosenOptionCode());
                }
            }
        }

        return answers;
    }
}
