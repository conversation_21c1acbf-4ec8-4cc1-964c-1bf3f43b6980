package com.yzedulife.util;

import com.yzedulife.service.dto.*;
import com.yzedulife.service.entity.*;
import com.yzedulife.vo.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 测试数据工厂类
 * 用于创建各种测试数据对象
 */
public class TestDataFactory {

    // ==================== 管理员相关测试数据 ====================
    
    public static AdminDTO createAdminDTO() {
        AdminDTO admin = new AdminDTO();
        admin.setId(1L);
        admin.setUsername("testadmin");
        admin.setPassword("e16b2ab8d12314bf4efbd6203906ea6c"); // MD5 of "testpassword"
        admin.setRole("admin");
        return admin;
    }

    public static AdminDTO createAdminDTO(Long id, String username, String password) {
        AdminDTO admin = createAdminDTO();
        admin.setId(id);
        admin.setUsername(username);
        admin.setPassword(password);
        return admin;
    }

    // ==================== 学生用户相关测试数据 ====================
    
    public static StudentUserDTO createStudentUserDTO() {
        StudentUserDTO student = new StudentUserDTO();
        student.setId(1L);
        student.setName("张三");
        student.setStudentNumber("2023001");
        student.setClassId(1L);
        return student;
    }

    public static StudentUserDTO createStudentUserDTO(Long id, String name, String studentNumber) {
        StudentUserDTO student = createStudentUserDTO();
        student.setId(id);
        student.setName(name);
        student.setStudentNumber(studentNumber);
        return student;
    }

    public static StudentUserDTO createStudentUserDTO(Long id, String name, String studentNumber, Long classId) {
        StudentUserDTO student = createStudentUserDTO();
        student.setId(id);
        student.setName(name);
        student.setStudentNumber(studentNumber);
        student.setClassId(classId);
        return student;
    }

    // ==================== 社会人士用户相关测试数据 ====================
    
    public static OtherUserDTO createOtherUserDTO() {
        OtherUserDTO otherUser = new OtherUserDTO();
        otherUser.setId(1L);
        otherUser.setPhone("13800138000");
        return otherUser;
    }

    public static OtherUserDTO createOtherUserDTO(Long id, String phone) {
        OtherUserDTO otherUser = createOtherUserDTO();
        otherUser.setId(id);
        otherUser.setPhone(phone);
        return otherUser;
    }

    // ==================== 验证码相关测试数据 ====================
    
    public static CodeDTO createCodeDTO() {
        return new CodeDTO("13800138000", "123456", LocalDateTime.now().plusMinutes(2));
    }

    public static CodeDTO createCodeDTO(String phone, String code, LocalDateTime expiry) {
        return new CodeDTO(phone, code, expiry);
    }

    public static CodeDTO createExpiredCodeDTO() {
        return new CodeDTO("13800138000", "123456", LocalDateTime.now().minusMinutes(1));
    }

    // ==================== 问卷相关测试数据 ====================
    
    public static QuestionnaireDTO createQuestionnaireDTO() {
        QuestionnaireDTO questionnaire = new QuestionnaireDTO();
        questionnaire.setId(1L);
        questionnaire.setTitle("测试问卷");
        questionnaire.setDescription("/images/questionnaire/test.jpg");
        questionnaire.setTargetAudience("STUDENT");
        questionnaire.setStatus(1);
        questionnaire.setCreatorId(1L);
        return questionnaire;
    }

    public static QuestionnaireVO createQuestionnaireVO() {
        QuestionnaireVO questionnaire = new QuestionnaireVO();
        questionnaire.setTitle("测试问卷");
        questionnaire.setDescription("/images/questionnaire/test.jpg");
        questionnaire.setTargetAudience("STUDENT");
        questionnaire.setStatus(1);
        return questionnaire;
    }

    // ==================== 问卷页面相关测试数据 ====================
    
    public static QuestionnairePageDTO createQuestionnairePageDTO() {
        QuestionnairePageDTO page = new QuestionnairePageDTO();
        page.setId(1L);
        page.setQuestionnaireId(1L);
        page.setPageNumber(1);
        return page;
    }

    // ==================== 题目相关测试数据 ====================
    
    public static QuestionDTO createQuestionDTO() {
        QuestionDTO question = new QuestionDTO();
        question.setId(1L);
        question.setPageId(1L);
        question.setContentType("TEXT");
        question.setContent("测试题目");
        question.setDisplayOrder(1);
        question.setCorrectAnswerCode("A");
        question.setCategory("测试分类");
        return question;
    }

    public static QuestionVO createQuestionVO() {
        QuestionVO question = new QuestionVO();
        question.setPageId(1L);
        question.setContentType("TEXT");
        question.setContent("测试题目");
        question.setDisplayOrder(1);
        question.setCorrectAnswerCode("A");
        question.setCategory("测试分类");

        // 添加选项
        List<QuestionOptionVO> options = new ArrayList<>();
        options.add(createQuestionOptionVO("A", "选项A"));
        options.add(createQuestionOptionVO("B", "选项B"));
        question.setOptions(options);

        return question;
    }

    // ==================== 题目选项相关测试数据 ====================
    
    public static QuestionOptionDTO createQuestionOptionDTO() {
        QuestionOptionDTO option = new QuestionOptionDTO();
        option.setId(1L);
        option.setQuestionId(1L);
        option.setOptionType("TEXT");
        option.setContent("选项A");
        option.setOptionCode("A");
        return option;
    }

    public static QuestionOptionVO createQuestionOptionVO(String code, String text) {
        QuestionOptionVO option = new QuestionOptionVO();
        option.setOptionType("TEXT");
        option.setContent(text);
        option.setOptionCode(code);
        return option;
    }

    // ==================== 答案相关测试数据 ====================
    
    public static AnswerSheetDTO createAnswerSheetDTO() {
        AnswerSheetDTO answerSheet = new AnswerSheetDTO();
        answerSheet.setId(1L);
        answerSheet.setQuestionnaireId(1L);
        answerSheet.setSubmitterType("STUDENT");
        answerSheet.setStudentUserId(1L);
        answerSheet.setSubmitTime(LocalDateTime.now());
        return answerSheet;
    }

    public static AnswerDetailDTO createAnswerDetailDTO() {
        AnswerDetailDTO answerDetail = new AnswerDetailDTO();
        answerDetail.setId(1L);
        answerDetail.setAnswerSheetId(1L);
        answerDetail.setQuestionId(1L);
        answerDetail.setChosenOptionCode("A");
        answerDetail.setIsCorrect(true);
        return answerDetail;
    }

    public static AnswerDetailVO createAnswerDetailVO() {
        AnswerDetailVO answerDetail = new AnswerDetailVO();
        answerDetail.setAnswerSheetId(1L);
        answerDetail.setQuestionId(1L);
        answerDetail.setChosenOptionCode("A");
        return answerDetail;
    }

    public static List<AnswerDetailVO> createAnswerDetailVOList() {
        List<AnswerDetailVO> answerDetails = new ArrayList<>();
        answerDetails.add(createAnswerDetailVO());

        AnswerDetailVO answerDetail2 = new AnswerDetailVO();
        answerDetail2.setAnswerSheetId(1L);
        answerDetail2.setQuestionId(2L);
        answerDetail2.setChosenOptionCode("B");
        answerDetails.add(answerDetail2);

        return answerDetails;
    }

    public static AnswerSubmitVO createAnswerSubmitVO() {
        AnswerSubmitVO answerSubmit = new AnswerSubmitVO();
        answerSubmit.setQuestionId(1L);
        answerSubmit.setChosenOptionCode("A");
        return answerSubmit;
    }

    public static List<AnswerSubmitVO> createAnswerSubmitVOList() {
        List<AnswerSubmitVO> answerSubmits = new ArrayList<>();
        answerSubmits.add(createAnswerSubmitVO());

        AnswerSubmitVO answerSubmit2 = new AnswerSubmitVO();
        answerSubmit2.setQuestionId(2L);
        answerSubmit2.setChosenOptionCode("B");
        answerSubmits.add(answerSubmit2);

        return answerSubmits;
    }

    // ==================== 学生班级相关测试数据 ====================

    public static StudentClassDTO createStudentClassDTO() {
        StudentClassDTO studentClass = new StudentClassDTO();
        studentClass.setId(1L);
        studentClass.setClassName("计算机科学与技术1班");
        studentClass.setSchoolId(1L);
        return studentClass;
    }

    public static StudentClassDTO createStudentClassDTO(Long id, String className) {
        StudentClassDTO studentClass = createStudentClassDTO();
        studentClass.setId(id);
        studentClass.setClassName(className);
        return studentClass;
    }

    public static StudentClassDTO createStudentClassDTO(Long id, String className, Long schoolId) {
        StudentClassDTO studentClass = createStudentClassDTO();
        studentClass.setId(id);
        studentClass.setClassName(className);
        studentClass.setSchoolId(schoolId);
        return studentClass;
    }

    public static StudentClass createStudentClass() {
        StudentClass studentClass = new StudentClass();
        studentClass.setId(1L);
        studentClass.setClassName("计算机科学与技术1班");
        studentClass.setSchoolId(1L);
        return studentClass;
    }

    // ==================== 学生学校相关测试数据 ====================

    public static StudentSchoolDTO createStudentSchoolDTO() {
        StudentSchoolDTO studentSchool = new StudentSchoolDTO();
        studentSchool.setId(1L);
        studentSchool.setSchoolName("测试大学");
        return studentSchool;
    }

    public static StudentSchoolDTO createStudentSchoolDTO(Long id, String schoolName) {
        StudentSchoolDTO studentSchool = createStudentSchoolDTO();
        studentSchool.setId(id);
        studentSchool.setSchoolName(schoolName);
        return studentSchool;
    }

    public static StudentSchool createStudentSchool() {
        StudentSchool studentSchool = new StudentSchool();
        studentSchool.setId(1L);
        studentSchool.setSchoolName("测试大学");
        return studentSchool;
    }

    public static StudentSchool createStudentSchool(Long id, String schoolName) {
        StudentSchool studentSchool = createStudentSchool();
        studentSchool.setId(id);
        studentSchool.setSchoolName(schoolName);
        return studentSchool;
    }


}
