package com.yzedulife.service;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.StudentImportDTO;

import java.io.InputStream;
import java.util.List;

/**
 * Excel处理服务接口
 */
public interface ExcelService {

    /**
     * 从Excel文件导入学生数据
     * @param inputStream Excel文件输入流
     * @param fileName 文件名
     * @return 导入结果列表
     */
    List<StudentImportDTO> importStudentsFromExcel(InputStream inputStream, String fileName) throws BusinessException;

    /**
     * 导出班级答题数据到Excel
     * @param classId 班级ID
     * @param questionnaireIds 问卷ID列表（5个问卷）
     * @return Excel文件路径
     */
    String exportClassAnswerData(Long classId, List<Long> questionnaireIds) throws BusinessException;
}
