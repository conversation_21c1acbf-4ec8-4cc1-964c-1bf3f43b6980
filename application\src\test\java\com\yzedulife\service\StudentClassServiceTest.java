package com.yzedulife.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.StudentClassDTO;
import com.yzedulife.service.entity.StudentClass;
import com.yzedulife.service.entity.StudentSchool;
import com.yzedulife.service.mapper.StudentClassMapper;
import com.yzedulife.service.mapper.StudentSchoolMapper;
import com.yzedulife.service.mapper.StudentUserMapper;
import com.yzedulife.service.service.impl.StudentClassServiceImpl;
import com.yzedulife.util.TestDataFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("学生班级服务测试")
class StudentClassServiceTest {

    @Mock
    private StudentClassMapper studentClassMapper;

    @Mock
    private StudentSchoolMapper studentSchoolMapper;

    @Mock
    private StudentUserMapper studentUserMapper;

    @InjectMocks
    private StudentClassServiceImpl studentClassService;

    private StudentClass testStudentClass;
    private StudentClassDTO testStudentClassDTO;
    private StudentSchool testStudentSchool;

    @BeforeEach
    void setUp() {
        testStudentClass = TestDataFactory.createStudentClass();
        testStudentClassDTO = TestDataFactory.createStudentClassDTO();
        testStudentSchool = TestDataFactory.createStudentSchool();
    }

    @Test
    @DisplayName("创建班级 - 成功")
    void create_Success() throws BusinessException {
        // Given
        when(studentSchoolMapper.selectById(1L)).thenReturn(testStudentSchool);
        when(studentClassMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(studentClassMapper.insert(any(StudentClass.class))).thenReturn(1);

        // When
        StudentClassDTO result = studentClassService.create(testStudentClassDTO);

        // Then
        assertNotNull(result);
        assertEquals(testStudentClassDTO.getClassName(), result.getClassName());
        assertEquals(testStudentClassDTO.getSchoolId(), result.getSchoolId());

        verify(studentSchoolMapper).selectById(1L);
        verify(studentClassMapper).selectOne(any(LambdaQueryWrapper.class));
        verify(studentClassMapper).insert(any(StudentClass.class));
    }

    @Test
    @DisplayName("创建班级 - 学校不存在")
    void create_SchoolNotExists() {
        // Given
        when(studentSchoolMapper.selectById(1L)).thenReturn(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> studentClassService.create(testStudentClassDTO));

        assertEquals("指定的学校不存在", exception.getMessage());
        verify(studentSchoolMapper).selectById(1L);
        verify(studentClassMapper, never()).selectOne(any(LambdaQueryWrapper.class));
        verify(studentClassMapper, never()).insert(any(StudentClass.class));
    }

    @Test
    @DisplayName("更新班级 - 成功")
    void update_Success() throws BusinessException {
        // Given
        StudentClassDTO updateDTO = new StudentClassDTO();
        updateDTO.setId(1L);
        updateDTO.setClassName("新班级名称");
        updateDTO.setSchoolId(2L);

        StudentSchool newSchool = new StudentSchool();
        newSchool.setId(2L);
        newSchool.setSchoolName("新学校");

        when(studentClassMapper.selectById(1L)).thenReturn(testStudentClass);
        when(studentSchoolMapper.selectById(2L)).thenReturn(newSchool);
        when(studentClassMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(studentClassMapper.updateById(any(StudentClass.class))).thenReturn(1);

        // When
        StudentClassDTO result = studentClassService.update(updateDTO);

        // Then
        assertNotNull(result);
        assertEquals("新班级名称", result.getClassName());
        assertEquals(2L, result.getSchoolId());

        verify(studentClassMapper).selectById(1L);
        verify(studentSchoolMapper).selectById(2L);
        verify(studentClassMapper).selectOne(any(LambdaQueryWrapper.class));
        verify(studentClassMapper).updateById(any(StudentClass.class));
    }

    @Test
    @DisplayName("更新班级 - 新学校不存在")
    void update_NewSchoolNotExists() {
        // Given
        StudentClassDTO updateDTO = new StudentClassDTO();
        updateDTO.setId(1L);
        updateDTO.setClassName("新班级名称");
        updateDTO.setSchoolId(999L); // 不存在的学校ID

        when(studentClassMapper.selectById(1L)).thenReturn(testStudentClass);
        when(studentSchoolMapper.selectById(999L)).thenReturn(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> studentClassService.update(updateDTO));

        assertEquals("指定的学校不存在", exception.getMessage());
        verify(studentClassMapper).selectById(1L);
        verify(studentSchoolMapper).selectById(999L);
        verify(studentClassMapper, never()).selectOne(any(LambdaQueryWrapper.class));
        verify(studentClassMapper, never()).updateById(any(StudentClass.class));
    }

    @Test
    @DisplayName("更新班级 - 班级不存在")
    void update_ClassNotExists() {
        // Given
        StudentClassDTO updateDTO = new StudentClassDTO();
        updateDTO.setId(999L);
        updateDTO.setClassName("新班级名称");

        when(studentClassMapper.selectById(999L)).thenReturn(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> studentClassService.update(updateDTO));

        assertEquals("班级不存在", exception.getMessage());
        verify(studentClassMapper).selectById(999L);
        verify(studentSchoolMapper, never()).selectById(any());
        verify(studentClassMapper, never()).updateById(any(StudentClass.class));
    }

    @Test
    @DisplayName("更新班级 - 只更新班级名称，不更新学校")
    void update_OnlyClassName() throws BusinessException {
        // Given
        StudentClassDTO updateDTO = new StudentClassDTO();
        updateDTO.setId(1L);
        updateDTO.setClassName("新班级名称");
        // 不设置schoolId，保持原有学校

        when(studentClassMapper.selectById(1L)).thenReturn(testStudentClass);
        when(studentClassMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(studentClassMapper.updateById(any(StudentClass.class))).thenReturn(1);

        // When
        StudentClassDTO result = studentClassService.update(updateDTO);

        // Then
        assertNotNull(result);
        assertEquals("新班级名称", result.getClassName());
        assertEquals(testStudentClass.getSchoolId(), result.getSchoolId()); // 保持原有学校ID

        verify(studentClassMapper).selectById(1L);
        verify(studentSchoolMapper, never()).selectById(any()); // 不需要验证学校
        verify(studentClassMapper).selectOne(any(LambdaQueryWrapper.class));
        verify(studentClassMapper).updateById(any(StudentClass.class));
    }

    @Test
    @DisplayName("创建班级 - 学校ID为空")
    void create_NullSchoolId() {
        // Given
        StudentClassDTO dto = new StudentClassDTO();
        dto.setClassName("测试班级");
        dto.setSchoolId(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> studentClassService.create(dto));

        assertEquals("学校ID不能为空", exception.getMessage());
        verify(studentSchoolMapper, never()).selectById(any());
        verify(studentClassMapper, never()).insert(any(StudentClass.class));
    }

    @Test
    @DisplayName("更新班级 - ID为空")
    void update_NullId() {
        // Given
        StudentClassDTO updateDTO = new StudentClassDTO();
        updateDTO.setId(null);
        updateDTO.setClassName("新班级名称");

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> studentClassService.update(updateDTO));

        assertEquals("班级ID不能为空", exception.getMessage());
        verify(studentClassMapper, never()).selectById(any());
        verify(studentClassMapper, never()).updateById(any(StudentClass.class));
    }
}
